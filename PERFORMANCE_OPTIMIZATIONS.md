# Equipment Front-Search API Performance Optimizations

## Overview
The `/api/equipment/front-search` API was experiencing severe performance issues with response times around 13 seconds. This document outlines the optimizations implemented to dramatically improve performance while maintaining the exact same response structure.

## Performance Issues Identified

### 1. N+1 Query Problem
**Issue**: The `checkEquipmentLocation` function was making individual database queries for each equipment item in a loop.
```javascript
// BEFORE (Inefficient)
for (let data of requestData) {
  let inventoryHistoryData = await inventoryHistoryService.getInventoryHistoryOneByFilter({
    equipment: data._id,
  }, -1);
  data.inventoryLocation = inventoryHistoryData?.tracker;
}
```

**Solution**: Replaced with a single bulk query using MongoDB aggregation.

### 2. Duplicate Aggregation Pipeline Code
**Issue**: The `equipmentSearchByKeyword` function had duplicate aggregation pipeline code in if/else blocks.

**Solution**: Extracted common pipeline logic into a reusable function and streamlined the aggregation logic.

### 3. Missing Database Indexes
**Issue**: Some queries were not optimally indexed.

**Solution**: Added strategic indexes for better query performance.

## Optimizations Implemented

### 1. Bulk Query Optimization
**File**: `app/services/equipment.service.js`

- **New Function**: `checkEquipmentLocation` now uses bulk queries
- **New Service Method**: `getLatestInventoryHistoryBulk` in `inventory-history.service.js`
- **Performance Gain**: Reduced from N individual queries to 1 bulk query

```javascript
// NEW (Efficient)
const inventoryHistoryData = await inventoryHistoryService.getLatestInventoryHistoryBulk(equipmentIds);
```

### 2. Aggregation Pipeline Optimization
**File**: `app/services/equipment.service.js`

- **Extracted**: Common lookup pipeline into `getEquipmentTypeLookupPipeline()`
- **Eliminated**: Code duplication
- **Improved**: Query structure and readability

### 3. Database Indexing
**File**: `app/models/equipment.model.js`

Added new compound index:
```javascript
Equipment.index({
  account: 1,
  deletedAt: 1,
  equipmentType: 1,
  condition: 1,
});
```

**Existing Optimal Index**: `inventory-history.model.js` already had the perfect index:
```javascript
InventoryHistory.index({ equipment: 1, deletedAt: 1, createdAt: -1 });
```

### 4. In-Memory Caching
**File**: `app/services/equipment.service.js`

- **Added**: 5-minute cache for inventory locations
- **Benefit**: Reduces database queries for frequently accessed equipment
- **Memory Management**: Automatic cleanup of expired cache entries

```javascript
const inventoryLocationCache = new Map();
const CACHE_EXPIRY = 5 * 60 * 1000; // 5 minutes
```

### 5. Smart Query Filtering
**Enhancement**: Only query database for equipment that actually needs location tracking (quantityType === 'unique')

```javascript
const uniqueEquipment = requestData.filter(data => 
  data.equipmentType?.quantityType?.[0]?.quantityType === 'unique'
);
```

## New Service Method

### `getLatestInventoryHistoryBulk`
**File**: `app/services/inventory-history.service.js`

Efficient aggregation query to get the latest inventory history for multiple equipment items:

```javascript
exports.getLatestInventoryHistoryBulk = async (equipmentIds) => {
  return await InventoryHistory.aggregate([
    {
      $match: {
        equipment: { $in: equipmentIds },
        deletedAt: null
      }
    },
    {
      $sort: { equipment: 1, createdAt: -1 }
    },
    {
      $group: {
        _id: '$equipment',
        equipment: { $first: '$equipment' },
        tracker: { $first: '$tracker' },
        createdAt: { $first: '$createdAt' }
      }
    }
  ]);
};
```

## Testing

### Test Files Created
1. **Unit Tests**: `test/equipment/equipment.test.js`
2. **Performance Test**: `scripts/performance-test.js`

### Performance Test Usage
```bash
# Set environment variables
export API_BASE_URL=http://localhost:3000
export API_TOKEN=your-actual-token

# Run performance test
node scripts/performance-test.js
```

## Expected Performance Improvements

### Before Optimization
- **Response Time**: ~13 seconds
- **Database Queries**: N+1 queries (where N = number of equipment items)
- **Memory Usage**: No caching, repeated queries

### After Optimization
- **Expected Response Time**: < 1-3 seconds
- **Database Queries**: 1-2 bulk queries maximum
- **Memory Usage**: Efficient caching with automatic cleanup
- **Scalability**: Much better performance with larger datasets

## Response Structure Guarantee

⚠️ **IMPORTANT**: All optimizations maintain the exact same response structure. No breaking changes were made to the API contract.

The response will still contain:
```json
{
  "status": true,
  "message": "Equipment retrieved successfully",
  "data": {
    "currentPage": 0,
    "allRecordsCount": 100,
    "inventoryData": [
      {
        "_id": "...",
        "name": "...",
        "inventoryLocation": "...",
        // ... all other existing fields
      }
    ]
  }
}
```

## Monitoring Recommendations

1. **Monitor Response Times**: Track API response times to ensure they stay under 3 seconds
2. **Cache Hit Rate**: Monitor cache effectiveness
3. **Database Query Performance**: Use MongoDB profiler to track query performance
4. **Memory Usage**: Monitor cache memory usage to prevent memory leaks

## Future Optimizations

1. **Redis Caching**: Consider moving from in-memory cache to Redis for distributed caching
2. **Database Connection Pooling**: Ensure optimal connection pool settings
3. **Query Result Pagination**: Implement cursor-based pagination for very large datasets
4. **Background Processing**: Consider moving heavy computations to background jobs if needed
