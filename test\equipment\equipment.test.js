const request = require('supertest');
const app = require('../../app/server');

describe('Equipment API Tests', () => {
  // You'll need to replace this with a valid token for your test environment
  const token = 'your-test-token-here';
  
  describe('GET /api/equipment/front-search', () => {
    it('should return equipment data with inventory locations', async () => {
      const response = await request(app)
        .get('/api/equipment/front-search')
        .set('Authorization', `Bearer ${token}`)
        .query({
          page: 0,
          perPage: 10
        });
      
      // Test should pass if API returns 200 and has expected structure
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('inventoryData');
      
      // Check if inventory data is an array
      expect(Array.isArray(response.body.data.inventoryData)).toBe(true);
      
      // If there are items, check they have inventoryLocation property
      if (response.body.data.inventoryData.length > 0) {
        const firstItem = response.body.data.inventoryData[0];
        expect(firstItem).toHaveProperty('inventoryLocation');
      }
    });

    it('should handle search parameters correctly', async () => {
      const response = await request(app)
        .get('/api/equipment/front-search')
        .set('Authorization', `Bearer ${token}`)
        .query({
          page: 0,
          perPage: 5,
          search: 'test'
        });
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', true);
    });

    it('should handle pagination correctly', async () => {
      const response = await request(app)
        .get('/api/equipment/front-search')
        .set('Authorization', `Bearer ${token}`)
        .query({
          page: 0,
          perPage: 5
        });
      
      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('currentPage', 0);
    });

    it('should return 401 without valid token', async () => {
      const response = await request(app)
        .get('/api/equipment/front-search')
        .query({
          page: 0,
          perPage: 10
        });
      
      expect(response.status).toBe(401);
    });
  });

  describe('Performance Test', () => {
    it('should respond within reasonable time (< 3 seconds)', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/equipment/front-search')
        .set('Authorization', `Bearer ${token}`)
        .query({
          page: 0,
          perPage: 50 // Test with larger dataset
        });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(3000); // Should be much faster than 13 seconds now
      
      console.log(`API response time: ${responseTime}ms`);
    });
  });
});
