const axios = require('axios');

/**
 * Performance test script for the equipment front-search API
 * This script tests the API performance after optimizations
 */

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const API_TOKEN = process.env.API_TOKEN || 'your-token-here';

async function testAPIPerformance() {
  console.log('🚀 Starting Equipment Front-Search API Performance Test...\n');

  const testCases = [
    { page: 0, perPage: 10, description: 'Small dataset (10 items)' },
    { page: 0, perPage: 25, description: 'Medium dataset (25 items)' },
    { page: 0, perPage: 50, description: 'Large dataset (50 items)' },
    { page: 0, perPage: 100, description: 'Extra large dataset (100 items)' }
  ];

  const results = [];

  for (const testCase of testCases) {
    console.log(`📊 Testing: ${testCase.description}`);
    
    try {
      const startTime = Date.now();
      
      const response = await axios.get(`${API_BASE_URL}/api/equipment/front-search`, {
        headers: {
          'Authorization': `Bearer ${API_TOKEN}`
        },
        params: {
          page: testCase.page,
          perPage: testCase.perPage
        }
      });
      
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      const result = {
        ...testCase,
        responseTime,
        status: response.status,
        itemCount: response.data?.data?.inventoryData?.length || 0,
        totalCount: response.data?.data?.allRecordsCount || 0
      };
      
      results.push(result);
      
      console.log(`   ✅ Status: ${result.status}`);
      console.log(`   ⏱️  Response Time: ${result.responseTime}ms`);
      console.log(`   📦 Items Returned: ${result.itemCount}`);
      console.log(`   📊 Total Available: ${result.totalCount}`);
      console.log('');
      
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
      console.log('');
    }
  }

  // Summary
  console.log('📈 Performance Test Summary:');
  console.log('=' .repeat(50));
  
  const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
  const maxResponseTime = Math.max(...results.map(r => r.responseTime));
  const minResponseTime = Math.min(...results.map(r => r.responseTime));
  
  console.log(`Average Response Time: ${avgResponseTime.toFixed(2)}ms`);
  console.log(`Fastest Response: ${minResponseTime}ms`);
  console.log(`Slowest Response: ${maxResponseTime}ms`);
  
  if (maxResponseTime < 3000) {
    console.log('🎉 EXCELLENT: All responses under 3 seconds!');
  } else if (maxResponseTime < 5000) {
    console.log('✅ GOOD: All responses under 5 seconds');
  } else {
    console.log('⚠️  WARNING: Some responses took longer than 5 seconds');
  }
  
  console.log('\n🔍 Detailed Results:');
  results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.description}: ${result.responseTime}ms (${result.itemCount} items)`);
  });
}

// Run the test
if (require.main === module) {
  testAPIPerformance().catch(console.error);
}

module.exports = { testAPIPerformance };
